import { requestClient } from '#/api/request';

export namespace ChatApi {
  /** 获取历史会话接口参数 */
  export interface GetDialogueListParams {
    type: number;
  }

  /** 历史会话数据项 */
  export interface DialogueItem {
    dialogue_id: string;
    first_content: string;
  }

  /** 获取提示词接口参数 */
  export interface GetCueWordParams {
    type: number;
  }

  /** 提示词数据 */
  export interface CueWordData {
    content: string;
    session_id: string;
  }

  /** 流式对话接口参数 */
  export interface StreamDialogueParams {
    content: string;
    session_id: string;
    type: number;
  }

  /** 流式对话响应数据 */
  export interface StreamDialogueResponse {
    data: string;
    session_id?: string;
  }

  /** 获取历史消息接口参数 */
  export interface GetDialogueHistoryParams {
    session_id: string;
    type: number;
  }

  /** 历史消息数据项 */
  export interface DialogueHistoryItem {
    content: string;
    id: number;
    img_url: string;
    role: 'assistant' | 'user';
  }

  /** 图片生成接口参数 */
  export interface GenerateImageParams {
    content: string;
    session_id: string;
    type: number;
  }

  /** 收藏接口参数 */
  export interface AddCollectParams {
    category: number; // 1文本2图片3视频
    content: string; // 文本、图片链接、视频链接
    type: number; // 当前会话的type
  }

  /** 图片编辑接口参数 */
  export interface EditImageParams {
    type: number; // 会话类型
    content: string; // 文字内容
    session_id: string; // 会话ID
    img_url: string; // 上传的图片链接
  }

  /** 图生视频接口参数 */
  export interface ImgToVideoParams {
    type: number; // 会话类型
    content: string; // 文字内容
    session_id: string; // 会话ID
    img_url: string; // 上传的图片链接
  }

  /** 图生视频接口返回值 */
  export interface ImgToVideoResult {
    id: string; // 任务ID
  }

  /** 任务查询接口参数 */
  export interface TaskQueryParams {
    task_id: string; // 任务ID
  }

  /** 任务查询接口返回值 */
  export interface TaskQueryResult {
    id: string;
    model: string;
    status: string;
    content: {
      video_url: string;
    };
    usage: {
      completion_tokens: number;
      total_tokens: number;
    };
    created_at: number;
    updated_at: number;
    seed: number;
    resolution: string;
    duration: number;
    ratio: string;
    framespersecond: number;
  }
}

/**
 * 获取历史会话列表
 * 注意：拦截器已处理响应，直接返回data字段，无需判断errno
 */
export async function getDialogueListApi(
  params: ChatApi.GetDialogueListParams,
) {
  return requestClient.post<ChatApi.DialogueItem[]>(
    '/mobile/ai_dialogue/getOneDialogueList',
    params,
  );
}

/**
 * 获取提示词
 * 注意：拦截器已处理响应，直接返回data字段，无需判断errno
 * 只在新会话初始化时调用，添加到第一条消息
 */
export async function getCueWordApi(params: ChatApi.GetCueWordParams) {
  return requestClient.post<ChatApi.CueWordData>(
    '/mobile/ai_dialogue/cueWord',
    params,
  );
}

/**
 * 获取历史消息记录
 * 注意：拦截器已处理响应，直接返回data字段，无需判断errno
 */
export async function getDialogueHistoryApi(
  params: ChatApi.GetDialogueHistoryParams,
) {
  return requestClient.post<ChatApi.DialogueHistoryItem[]>(
    '/mobile/ai_dialogue/getOneDialogueHistory',
    params,
  );
}

/**
 * 获取流式对话接口URL
 * 注意：此接口返回流式数据，需要使用流式请求工具处理
 */
export function getStreamDialogueUrl(): string {
  return '/mobile/ai_dialogue/start';
}

/**
 * 获取AI设计师专用流式对话接口URL
 * 注意：此接口返回流式数据，需要使用流式请求工具处理
 */
export function getDesignerDialogueUrl(): string {
  return '/mobile/ai_dialogue/designer';
}

/**
 * AI图片生成接口
 * 注意：此接口不是流式的，直接返回结果
 */
export async function generateImageApi(params: ChatApi.GenerateImageParams) {
  return requestClient.post<string>('/mobile/ai_text/generationsImg', params);
}

/**
 * 添加收藏接口
 * 注意：拦截器已处理响应，直接返回data字段，无需判断errno
 */
export async function addCollectApi(params: ChatApi.AddCollectParams) {
  return requestClient.post<any>('/mobile/user_collect/add', params);
}

/**
 * 图片编辑接口
 * 注意：此接口返回的data字段包含生成的图片链接
 */
export async function editImageApi(params: ChatApi.EditImageParams) {
  return requestClient.post<string>('/mobile/ai_text/editImg', params);
}

/**
 * 图生视频接口
 * 注意：此接口返回的data字段包含任务ID
 */
export async function imgToVideoApi(params: ChatApi.ImgToVideoParams) {
  return requestClient.post<ChatApi.ImgToVideoResult>('/mobile/ai_text/imgToVideo', params);
}

/**
 * 任务查询接口
 * 注意：用于轮询查询视频生成任务状态
 */
export async function taskQueryApi(params: ChatApi.TaskQueryParams) {
  return requestClient.post<ChatApi.TaskQueryResult>('/mobile/ai_text/taskList', params);
}
