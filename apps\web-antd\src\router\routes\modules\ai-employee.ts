import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:bot',
      order: 5,
      title: $t('page.menu.aiEmployee.title'),
    },
    name: 'AIEmployee',
    path: '/ai-employee',
    redirect: '/ai-employee/ai-designer',
    children: [
      {
        meta: {
          icon: 'lucide:palette',
          title: $t('page.menu.aiEmployee.aiDesigner'),
          query: {
            title: 'AI设计师',
            type: '14',
          },
        },
        name: 'AIDesigner',
        path: '/ai-employee/ai-designer',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI设计师',
                type: to.query.type || '14',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:camera',
          title: $t('page.menu.aiEmployee.aiIDPhoto'),
        },
        name: 'AIIDPhoto',
        path: '/ai-employee/ai-id-photo',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:image',
          title: $t('page.menu.aiEmployee.aiPhotoRestoration'),
        },
        name: 'AIPhotoRestoration',
        path: '/ai-employee/ai-photo-restoration',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:mic',
          title: $t('page.menu.aiEmployee.aiMeetingRecord'),
        },
        name: 'AIMeetingRecord',
        path: '/ai-employee/ai-meeting-record',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:languages',
          title: $t('page.menu.aiEmployee.aiTranslator'),
          query: {
            title: 'AI翻译官',
            type: '16',
          },
        },
        name: 'AITranslator',
        path: '/ai-employee/ai-translator',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI翻译官',
                type: to.query.type || '16',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:user-check',
          title: $t('page.menu.aiEmployee.aiInterviewer'),
        },
        name: 'AIInterviewer',
        path: '/ai-employee/ai-interviewer',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:scale',
          title: $t('page.menu.aiEmployee.aiLegalAdvisor'),
          query: {
            title: 'AI法律顾问',
            type: '8',
          },
        },
        name: 'AILegalAdvisor',
        path: '/ai-employee/ai-legal-advisor',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI法律顾问',
                type: to.query.type || '8',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:calculator',
          title: $t('page.menu.aiEmployee.aiFinancialAnalysis'),
        },
        name: 'AIFinancialAnalysis',
        path: '/ai-employee/ai-financial-analysis',
        component: () => import('#/views/common/under-development.vue'),
      },
    ],
  },
];

export default routes;
