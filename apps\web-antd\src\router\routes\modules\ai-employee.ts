import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:bot',
      order: 5,
      title: $t('page.menu.aiEmployee.title'),
    },
    name: 'AIEmployee',
    path: '/ai-employee',
    redirect: '/ai-employee/ai-designer',
    children: [
      {
        meta: {
          icon: 'lucide:palette',
          title: $t('page.menu.aiEmployee.aiDesigner'),
          query: {
            title: 'AI设计师',
            type: '14',
          },
        },
        name: 'AIDesigner',
        path: '/ai-employee/ai-designer',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI设计师',
                type: to.query.type || '14',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:mic',
          title: $t('page.menu.aiEmployee.aiMeetingRecord'),
        },
        name: 'AIMeetingRecord',
        path: '/ai-employee/ai-meeting-record',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:languages',
          title: $t('page.menu.aiEmployee.aiTranslator'),
          query: {
            title: 'AI翻译官',
            type: '16',
          },
        },
        name: 'AITranslator',
        path: '/ai-employee/ai-translator',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI翻译官',
                type: to.query.type || '16',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:image',
          title: '图片精修',
          query: {
            title: '图片精修',
            type: '17',
          },
        },
        name: 'AIImageRetouch',
        path: '/ai-employee/ai-image-retouch',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || '图片精修',
                type: to.query.type || '17',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:smile',
          title:'表情包',
          query: {
            title: '表情包',
            type: '18',
          },
        },
        name: 'AIEmoji',
        path: '/ai-employee/ai-emoji',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || '表情包',
                type: to.query.type || '18',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:scissors',
          title: '商品抠图',
          query: {
            title: '商品抠图',
            type: '19',
          },
        },
        name: 'AIProductCutout',
        path: '/ai-employee/ai-product-cutout',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || '商品抠图',
                type: to.query.type || '19',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:scale',
          title: $t('page.menu.aiEmployee.aiLegalAdvisor'),
          query: {
            title: 'AI法律顾问',
            type: '8',
          },
        },
        name: 'AILegalAdvisor',
        path: '/ai-employee/ai-legal-advisor',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI法律顾问',
                type: to.query.type || '8',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:video',
          title: 'AI图生视频',
          query: {
            title: 'AI图生视频',
            type: '20',
          },
        },
        name: 'AIImageToVideo',
        path: '/ai-employee/ai-image-to-video',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || 'AI图生视频',
                type: to.query.type || '20',
              },
            });
          } else {
            next();
          }
        },
      },
    ],
  },
];

export default routes;
