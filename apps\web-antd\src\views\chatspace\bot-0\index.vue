<script setup lang="ts">
import type {
  AttachmentsProps,
  BubbleListProps,
  ConversationsProps,
} from 'ant-design-x-vue';
import type { UploadFile } from 'ant-design-vue';

// import type { VNode } from 'vue';
import type { ChatA<PERSON> } from '#/api';

import {
  computed,
  h,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { useRoute } from 'vue-router';

import {
  CloudUploadOutlined,
  // HeartFilled,
  // HeartOutlined,
  PlusOutlined,
  // StarFilled,
  StarOutlined,
  PaperClipOutlined,
  // SmileOutlined,
  UserOutlined,
} from '@ant-design/icons-vue';
import { Badge, Button, Flex, Modal, Space, theme, Typography, Upload, message } from 'ant-design-vue';
import {
  Attachments,
  Bubble,
  Conversations,
  Sender,
  useXAgent,
  useXChat,
  Welcome,
} from 'ant-design-x-vue';
import markdownit from 'markdown-it';
import markdownItMultimdTable from 'markdown-it-multimd-table';

import {
  addCollectApi,
  editImageApi,
  generateImageApi,
  getCueWordApi,
  getDesignerDialogueUrl,
  getDialogueHistoryApi,
  getDialogueListApi,
  getStreamDialogueUrl,
} from '#/api';
import { createStreamHandler } from '#/utils/stream-request';

defineOptions({ name: 'PlaygroundIndependentSetup' });

const { token } = theme.useToken();

// 配置markdown解析器
const md = markdownit({ html: true, breaks: true }).use(
  markdownItMultimdTable,
  {
    multiline: true,
    rowspan: true,
    headerless: true,
  },
);

// markdown渲染函数 - 使用更简单的实现
const renderMarkdown = (content: string) => {
  const renderedHtml = md.render(content);

  // 使用更简单的div包装，添加markdown样式类
  return h('div', {
    innerHTML: renderedHtml,
    class: 'markdown-content',
    style: {
      lineHeight: '1.6',
      wordBreak: 'break-word',
    },
    // 使用事件委托处理图片点击
    onClick: (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'IMG') {
        event.preventDefault();
        openImagePreview({
          src: target.getAttribute('src') || '',
          alt: target.getAttribute('alt') || '',
          title: target.getAttribute('title') || '',
        });
      }
    },
  });
};
const route = useRoute();

// 获取路由查询参数
const routeTitle = computed(() => (route.query.title as string) || '');
const routeType = computed(() => (route.query.type as string) || '');

// 响应式屏幕尺寸检测
const isSmallScreen = ref(false);
const screenWidth = ref(window.innerWidth);

const updateScreenSize = () => {
  screenWidth.value = window.innerWidth;
  isSmallScreen.value = screenWidth.value < 900;
};

onMounted(() => {
  window.addEventListener('resize', updateScreenSize);
  updateScreenSize();
  // 获取历史会话列表
  fetchDialogueList();
  // 获取提示词
  fetchCueWord();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateScreenSize);
});

const styles = computed(() => {
  // 响应式布局调整
  const isSmall = isSmallScreen.value;

  return {
    layout: {
      width: '100%',
      height: '100%',
      display: 'flex',
      'flex-direction': isSmall ? 'column' : 'row',
      background: `${token.value.colorBgContainer}`,
      'font-family': `AlibabaPuHuiTi, ${token.value.fontFamily}, sans-serif`,
      'border-radius': `${token.value.borderRadius}px`,
      overflow: 'hidden',
      'min-height': '500px',
      'max-height': 'calc(100vh - 88px)',
    },
    menu: {
      background: `${token.value.colorBgLayout}80`,
      width: isSmall ? '100%' : '280px',
      'min-width': isSmall ? '100%' : '280px',
      height: isSmall ? 'auto' : '100%',
      display: 'flex',
      'flex-direction': 'column',
      'border-left': isSmall ? 'none' : `1px solid ${token.value.colorBorder}`,
      'border-bottom': isSmall
        ? `1px solid ${token.value.colorBorder}`
        : 'none',
    },
    conversations: {
      padding: '0 12px',
      flex: 1,
      'overflow-y': 'auto',
      'max-height': isSmall ? '200px' : 'none',
    },
    chat: {
      height: '100%',
      width: isSmall ? '100%' : 'calc(100% - 280px)',
      'box-sizing': 'border-box',
      display: 'flex',
      'flex-direction': 'column',
      padding: isSmall ? '10px' : `${token.value.paddingLG}px`,
      gap: '16px',
      overflow: 'hidden',
      order: isSmall ? 1 : -1, // 在小屏幕时保持原顺序，大屏幕时聊天区域在前
    },
    messages: {
      flex: 1,
    },
    placeholder: {
      'padding-top': '32px',
      'text-align': 'left',
      flex: 1,
    },
    sender: {
      'box-shadow': token.value.boxShadow,
      'margin-top': 'auto',
    },
    logo: {
      display: 'flex',
      height: '72px',
      'min-height': '72px',
      'align-items': 'center',
      'justify-content': 'start',
      padding: '0 24px',
      'box-sizing': 'border-box',
    },
    'logo-img': {
      width: '24px',
      height: '24px',
      display: 'inline-block',
    },
    'logo-span': {
      display: 'inline-block',
      margin: '0 8px',
      'font-weight': 'bold',
      color: token.value.colorText,
      'font-size': '16px',
      'white-space': 'nowrap',
    },
    addBtn: {
      background: '#1677ff0f',
      border: '1px solid #1677ff34',
      width: 'calc(100% - 24px)',
      margin: '0 12px 24px 12px',
    },
  } as const;
});

// function renderTitle(icon: VNode, title: string) {
//   return h(Space, { align: 'start' }, () => [icon, h('span', title)]);
// }

const defaultConversationsItems = [
  {
    key: 'new',
    label: '新建会话',
  },
];

// const placeholderPromptsItems: PromptsProps['items'] = [
//   {
//     key: '1',
//     label: renderTitle(
//       h(FireOutlined, { style: { color: '#FF4D4F' } }),
//       'Hot Topics',
//     ),
//     description: 'What are you interested in?',
//     children: [
//       {
//         key: '1-1',
//         description: `What's new in X?`,
//       },
//       {
//         key: '1-2',
//         description: `What's AGI?`,
//       },
//       {
//         key: '1-3',
//         description: `Where is the doc?`,
//       },
//     ],
//   },
//   {
//     key: '2',
//     label: renderTitle(
//       h(ReadOutlined, { style: { color: '#1890FF' } }),
//       'Design Guide',
//     ),
//     description: 'How to design a good product?',
//     children: [
//       {
//         key: '2-1',
//         icon: h(HeartOutlined),
//         description: `Know the well`,
//       },
//       {
//         key: '2-2',
//         icon: h(SmileOutlined),
//         description: `Set the AI role`,
//       },
//       {
//         key: '2-3',
//         icon: h(CommentOutlined),
//         description: `Express the feeling`,
//       },
//     ],
//   },
// ];

// const senderPromptsItems: PromptsProps['items'] = [
//   {
//     key: '1',
//     description: 'Hot Topics',
//     icon: h(FireOutlined, { style: { color: '#FF4D4F' } }),
//   },
//   {
//     key: '2',
//     description: 'Design Guide',
//     icon: h(ReadOutlined, { style: { color: '#1890FF' } }),
//   },
// ];

const roles: BubbleListProps['roles'] = {
  ai: {
    placement: 'start',
    avatar: { icon: h(UserOutlined), style: { background: '#fde3cf' } },
    typing: { step: 2, interval: 50 },
    messageRender: renderMarkdown,
    footer: (content: any) => {
      const messageId = `ai_${Date.now()}_${Math.random()}`;
      const isCollecting = collectingMessages.value.has(messageId);
      const contentStr = typeof content === 'string' ? content : String(content);

      // 判断是否应该显示收藏按钮
      if (!shouldShowCollectButton(contentStr)) {
        return h('div');
      }

      return h('div', {
        style: {
          marginTop: '8px',
          display: 'flex',
          justifyContent: 'flex-end',
          paddingRight: '8px'
        }
      }, [
        h(Button, {
          type: 'text',
          size: 'small',
          icon: h(StarOutlined),
          loading: isCollecting,
          onClick: () => handleCollectMessage(messageId, contentStr),
          style: {
            color: '#1890ff',
            backgroundColor: '#f6f8fa',
            border: '1px solid #e1e4e8',
            borderRadius: '6px',
            padding: '4px 8px',
            height: 'auto',
            fontSize: '12px',
            transition: 'all 0.2s ease',
          },
          onMouseenter: (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            target.style.color = '#ffffff';
            target.style.backgroundColor = '#1890ff';
            target.style.borderColor = '#1890ff';
          },
          onMouseleave: (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            target.style.color = '#1890ff';
            target.style.backgroundColor = '#f6f8fa';
            target.style.borderColor = '#e1e4e8';
          },
        }, () => '收藏'),
      ]);
    },
  },
  ai_history: {
    placement: 'start',
    avatar: { icon: h(UserOutlined), style: { background: '#fde3cf' } },
    messageRender: renderMarkdown,
    // 历史消息不使用typing效果
    footer: (content: any) => {
      const messageId = `ai_history_${Date.now()}_${Math.random()}`;
      const isCollecting = collectingMessages.value.has(messageId);
      const contentStr = typeof content === 'string' ? content : String(content);

      // 判断是否应该显示收藏按钮
      if (!shouldShowCollectButton(contentStr)) {
        return h('div');
      }

      return h('div', {
        style: {
          marginTop: '8px',
          display: 'flex',
          justifyContent: 'flex-end',
          paddingRight: '8px'
        }
      }, [
        h(Button, {
          type: 'text',
          size: 'small',
          icon: h(StarOutlined),
          loading: isCollecting,
          onClick: () => handleCollectMessage(messageId, contentStr),
          style: {
            color: '#1890ff',
            backgroundColor: '#f6f8fa',
            border: '1px solid #e1e4e8',
            borderRadius: '6px',
            padding: '4px 8px',
            height: 'auto',
            fontSize: '12px',
            transition: 'all 0.2s ease',
          },
          onMouseenter: (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            target.style.color = '#ffffff';
            target.style.backgroundColor = '#1890ff';
            target.style.borderColor = '#1890ff';
          },
          onMouseleave: (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            target.style.color = '#1890ff';
            target.style.backgroundColor = '#f6f8fa';
            target.style.borderColor = '#e1e4e8';
          },
        }, () => '收藏'),
      ]);
    },
  },
  ai_image: {
    placement: 'start',
    avatar: { icon: h(UserOutlined), style: { background: '#fde3cf' } },
    messageRender: renderMarkdown,
    // 图片消息不使用typing效果，立即显示
    footer: (content: any) => {
      const messageId = `ai_image_${Date.now()}_${Math.random()}`;
      const isCollecting = collectingMessages.value.has(messageId);
      const contentStr = typeof content === 'string' ? content : String(content);

      // 判断是否应该显示收藏按钮
      if (!shouldShowCollectButton(contentStr)) {
        return h('div');
      }

      return h('div', {
        style: {
          marginTop: '8px',
          display: 'flex',
          justifyContent: 'flex-end',
          paddingRight: '8px'
        }
      }, [
        h(Button, {
          type: 'text',
          size: 'small',
          icon: h(StarOutlined),
          loading: isCollecting,
          onClick: () => handleCollectMessage(messageId, contentStr),
          style: {
            color: '#1890ff',
            backgroundColor: '#f6f8fa',
            border: '1px solid #e1e4e8',
            borderRadius: '6px',
            padding: '4px 8px',
            height: 'auto',
            fontSize: '12px',
            transition: 'all 0.2s ease',
          },
          onMouseenter: (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            target.style.color = '#ffffff';
            target.style.backgroundColor = '#1890ff';
            target.style.borderColor = '#1890ff';
          },
          onMouseleave: (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            target.style.color = '#1890ff';
            target.style.backgroundColor = '#f6f8fa';
            target.style.borderColor = '#e1e4e8';
          },
        }, () => '收藏'),
      ]);
    },
  },
  local: {
    placement: 'end',
    avatar: { icon: h(UserOutlined), style: { background: '#87d068' } },
  },
  local_with_image: {
    placement: 'end',
    avatar: { icon: h(UserOutlined), style: { background: '#87d068' } },
    messageRender: renderMarkdown,
  },
};

// ==================== State ====================
const headerOpen = ref(false);
const content = ref('');
const conversationsItems = ref(defaultConversationsItems);
const activeKey = ref(defaultConversationsItems[0].key);
const attachedFiles = ref<AttachmentsProps['items']>([]);
const agentRequestLoading = ref(false);

// 图片上传相关状态
const imageUploading = ref(false);
const uploadedImage = ref<UploadFile | null>(null);
const imageFileList = ref<UploadFile[]>([]);

// 判断是否需要显示图片上传功能
const shouldShowImageUpload = computed(() => {
  const type = Number.parseInt(routeType.value) || 1;
  return [17, 18, 19, 20].includes(type);
});

// 判断是否可以只发送图片（type=18表情包功能）
const canSendImageOnly = computed(() => {
  const type = Number.parseInt(routeType.value) || 1;
  return type === 18;
});

// 提示词
const cueWord = ref('');
// 会话ID
const sessionId = ref('');
// 会话ID映射表，key为会话key，value为session_id
const sessionIdMap = ref<Record<string, string>>({});

// 图片预览相关状态
const imagePreviewVisible = ref(false);
const currentPreviewImage = ref({
  src: '',
  alt: '',
  title: '',
});

// 收藏相关状态
const collectingMessages = ref<Set<string>>(new Set());

// 图片链接提取工具函数
const extractImageUrl = (content: string): string => {
  // 提取img标签中的src属性
  const imgRegex = /<img[^>]+src="([^"]+)"/i;
  const imgMatch = content.match(imgRegex);
  if (imgMatch && imgMatch[1]) {
    return imgMatch[1];
  }

  // 提取markdown格式的图片链接
  const markdownRegex = /!\[.*?\]\(([^)]+)\)/;
  const markdownMatch = content.match(markdownRegex);
  if (markdownMatch && markdownMatch[1]) {
    return markdownMatch[1];
  }

  return content; // 如果没有找到图片链接，返回原内容
};

// 判断消息内容类型
const getMessageContentType = (content: string): 'text' | 'image' | 'video' => {
  if (content.includes('<img') || content.includes('![')) {
    return 'image';
  }
  if (content.includes('<video') || content.includes('.mp4') || content.includes('.avi')) {
    return 'video';
  }
  return 'text';
};

// 判断是否应该显示收藏按钮
const shouldShowCollectButton = (content: string): boolean => {
  const currentType = Number.parseInt(routeType.value) || 1;
  const contentType = getMessageContentType(content);

  // type为20：只有视频消息显示收藏
  if (currentType === 20) {
    return contentType === 'video';
  }

  // type为[14, 17, 18, 19]：只有图片消息显示收藏
  if ([14, 17, 18, 19].includes(currentType)) {
    return contentType === 'image';
  }

  // 其他type：只有文本消息显示收藏
  if (![14, 17, 18, 19, 20].includes(currentType)) {
    return contentType === 'text';
  }

  return false;
};

// 滚动到消息列表底部
const scrollToBottom = () => {
  // 尝试多种方式查找滚动容器
  const selectors = [
    '.ant-bubble-list', // Bubble.List组件的内部容器
    '.ant-bubble-list-container', // 可能的容器类名
    '[data-scroll="true"]', // 带有滚动标识的元素
    '[style*="overflow-y: auto"]', // 有垂直滚动的元素
    '[style*="overflow: auto"]', // 有滚动的元素
  ];

  for (const selector of selectors) {
    const container = document.querySelector(selector);
    if (container) {
      container.scrollTop = container.scrollHeight;
      return;
    }
  }

  console.warn('未找到滚动容器，尝试滚动整个页面');
  // 最后的备用方案：滚动整个页面
  window.scrollTo(0, document.body.scrollHeight);
};

// 图片预览处理函数
const openImagePreview = (imageInfo: {
  alt: string;
  src: string;
  title: string;
}) => {
  currentPreviewImage.value = imageInfo;
  imagePreviewVisible.value = true;
};

const closeImagePreview = () => {
  imagePreviewVisible.value = false;
  currentPreviewImage.value = {
    src: '',
    alt: '',
    title: '',
  };
};

// 解析设计师JSON数据
const parseDesignerJson = (text: string) => {
  try {
    // 首先尝试直接将整个文本解析为JSON
    const jsonData = JSON.parse(text);

    // 如果解析成功，检查是否有content字段
    if (jsonData && jsonData.content) {
      // 可以在这里添加提示信息，如：正在生成科技感logo图片！
      getAiImg(jsonData.content);
    }
  } catch {
    // 如果直接解析失败，尝试从文本中提取JSON部分
    try {
      // 尝试用正则表达式匹配JSON格式的内容
      const jsonRegex = /\{[\s\S]*?\}/;
      const match = text.match(jsonRegex);

      if (match && match[0]) {
        // 尝试解析提取出的JSON字符串
        const jsonData = JSON.parse(match[0]);

        if (jsonData && jsonData.content) {
          getAiImg(jsonData.content);
        }
      } else {
        console.error('未找到有效的JSON数据');
      }
    } catch (extractError) {
      console.error('解析设计师数据失败:', extractError);
    }
  }
};

// AI图片生成函数
const getAiImg = async (content: string) => {
  // 先添加"正在生成中..."的提示消息
  const loadingMessageId = `ai_image_loading_${Date.now()}`;
  const loadingMessage = {
    id: loadingMessageId,
    message: '<div class="image-loading">🎨 正在生成图片中...</div>',
    status: 'success' as const,
    isImageMessage: true, // 使用ai_image角色，无打字效果
  };

  try {
    // 添加加载提示到消息列表
    const currentMessages = messages.value;
    setMessages([...currentMessages, loadingMessage]);

    // 滚动到底部
    await nextTick();
    scrollToBottom();

    const type = Number.parseInt(routeType.value) || 1;
    const response = await generateImageApi({
      content,
      session_id: sessionId.value,
      type,
    });

    // 将生成的图片替换加载提示消息
    if (response) {
      // 更新消息列表，将加载提示替换为实际图片
      const updatedMessages = messages.value.map((msg) => {
        if (msg.id === loadingMessageId) {
          return {
            ...msg,
            message: `<p align="center"><img src="${response}" alt="生成的图片"/></p>`,
          };
        }
        return msg;
      });

      setMessages(updatedMessages);

      // 滚动到底部
      await nextTick();
      scrollToBottom();
    }
  } catch (error) {
    console.error('图片生成失败:', error);

    // 图片生成失败时，更新加载提示为错误信息
    const updatedMessages = messages.value.map((msg) => {
      if (msg.id === loadingMessageId) {
        return {
          ...msg,
          message: '图片生成失败，请重试',
        };
      }
      return msg;
    });

    setMessages(updatedMessages);
    // 可以添加错误提示
    // message.error('图片生成失败，请重试');
  }
};

// 历史会话相关状态
const dialogueListLoading = ref(false);
const dialogueList = ref<ChatApi.DialogueItem[]>([]);

// ==================== Runtime ====================
// 使用useXAgent创建agent
const [agent] = useXAgent({
  request: async ({ message }, { onUpdate, onSuccess, onError }) => {
    if (!sessionId.value) {
      console.error('session_id未获取，无法发送消息');
      onError(new Error('session_id未获取'));
      return;
    }

    agentRequestLoading.value = true;

    try {
      const streamHandler = createStreamHandler();
      const type = Number.parseInt(routeType.value) || 1;

      // 根据type判断使用哪个接口
      // type=14 是AI设计师，使用专门的设计师接口
      const dialogueUrl =
        type === 14 ? getDesignerDialogueUrl() : getStreamDialogueUrl();

      let accumulatedContent = '';

      await streamHandler.request({
        url: dialogueUrl,
        method: 'POST',
        body: {
          content: message,
          session_id: sessionId.value,
          type,
        },
        onData: (data: string) => {
          // 累积内容并实时更新，确保保留换行符
          accumulatedContent += `${data}\n`;
          onUpdate(accumulatedContent);
        },
        onComplete: () => {
          // 完成时调用onSuccess
          onSuccess(accumulatedContent);
          agentRequestLoading.value = false;

          // 如果是AI设计师(type=14)，尝试解析JSON并生成图片
          if (type === 14) {
            parseDesignerJson(accumulatedContent);
          }
        },
        onError: (error: Error) => {
          console.error('流式对话失败:', error);
          onError(error);
          agentRequestLoading.value = false;
        },
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      onError(error instanceof Error ? error : new Error('发送消息失败'));
      agentRequestLoading.value = false;
    }
  },
});

// 使用useXChat管理消息
const { onRequest, messages, setMessages } = useXChat({
  agent: agent.value!,
  requestPlaceholder: '正在思考中...',
  requestFallback: '抱歉，发生了错误，请重试。',
});

watch(
  activeKey,
  () => {
    if (activeKey.value !== undefined) {
      setMessages([]);
      // 清空图片上传状态
      uploadedImage.value = null;
      imageFileList.value = [];
    }
  },
  { immediate: true },
);

// ==================== API Functions ====================
// 收藏消息处理函数
const handleCollectMessage = async (messageId: string, content: string) => {
  if (collectingMessages.value.has(messageId)) {
    return; // 防止重复收藏
  }

  try {
    collectingMessages.value.add(messageId);

    // 判断消息类型并提取正确的内容
    let category = 1; // 默认文本
    let collectContent = content;
    const contentType = getMessageContentType(content);

    if (contentType === 'image') {
      category = 2; // 图片
      collectContent = extractImageUrl(content); // 提取图片链接
    } else if (contentType === 'video') {
      category = 3; // 视频
      // 对于视频，可能需要提取视频链接，这里暂时使用原内容
      collectContent = content;
    }

    const type = Number.parseInt(routeType.value) || 1;

    await addCollectApi({
      category,
      content: collectContent,
      type,
    });

    // 收藏成功提示
    message.success('收藏成功');
  } catch (error) {
    console.error('收藏失败:', error);
    message.error('收藏失败，请重试');
  } finally {
    collectingMessages.value.delete(messageId);
  }
};

// 获取历史会话列表
const fetchDialogueList = async () => {
  try {
    dialogueListLoading.value = true;
    const type = Number.parseInt(routeType.value) || 1;
    const response = await getDialogueListApi({ type });
    dialogueList.value = response;

    // 将历史会话转换为会话列表格式，保留dialogue_id用于后续获取历史消息
    const historyItems = response.map((item) => ({
      key: item.dialogue_id,
      label: item.first_content,
      dialogue_id: item.dialogue_id, // 保留dialogue_id
    }));

    // 添加新建会话选项
    const newConversationItem = {
      key: 'new',
      label: '新建会话',
    };

    // 更新会话列表，包含新建会话和历史会话
    conversationsItems.value = [newConversationItem, ...historyItems];

    // 历史会话加载成功
  } catch (error) {
    console.error('获取历史会话失败:', error);
  } finally {
    dialogueListLoading.value = false;
  }
};

// 获取提示词
const fetchCueWord = async () => {
  try {
    const type = Number.parseInt(routeType.value) || 1;
    const response = await getCueWordApi({ type });
    cueWord.value = response.content;
    sessionId.value = response.session_id;
  } catch (error) {
    console.error('获取提示词失败:', error);
  }
};

// 获取历史消息记录
const fetchDialogueHistory = async (dialogueSessionId: string) => {
  try {
    const type = Number.parseInt(routeType.value) || 1;
    const response = await getDialogueHistoryApi({
      session_id: dialogueSessionId,
      type,
    });

    // 转换历史消息格式
    const historyMessages = response.map((item, index) => {
      let messageContent = item.content;

      // 如果是AI设计师(type=14)且有img_url字段，使用图片格式
      if (type === 14 && item.role === 'assistant' && item.img_url) {
        messageContent = `<p align="center"><img src="${item.img_url}" alt="生成的图片"/></p>`;
      }

      // 如果是用户消息且同时有content和img_url，需要同时显示文字和图片
      if (item.role === 'user' && item.content && item.img_url) {
        messageContent = `${item.content}<br/><img src="${item.img_url}" alt="用户上传的图片" style="max-width: 200px; margin-top: 8px; border-radius: 8px;"/>`;
      }

      return {
        id: `history_${item.id}_${index}`,
        message: messageContent,
        status:
          item.role === 'user' ? ('local' as const) : ('success' as const),
        isHistoryMessage: true, // 标识为历史消息
        // 如果是图片消息，标识为图片消息以使用正确的角色
        isImageMessage:
          type === 14 && item.role === 'assistant' && item.img_url
            ? true
            : undefined,
        // 保存原始的img_url用于后续处理
        img_url: item.img_url,
      };
    });

    // 设置历史消息
    setMessages(historyMessages);

    // 等待DOM更新完成后滚动到底部
    await nextTick();
    scrollToBottom();
  } catch (error) {
    console.error('获取历史消息失败:', error);
    // 如果获取历史消息失败，清空消息列表
    setMessages([]);
  }
};

// ==================== Event ====================
function onSubmit(nextContent: string) {
  const type = Number.parseInt(routeType.value) || 1;

  // 对于图片编辑功能（type 17、18、19），需要特殊处理
  if ([17, 18, 19].includes(type)) {
    // 验证输入
    const hasImage = uploadedImage.value && uploadedImage.value.url;
    const hasText = nextContent.trim();

    // type=18（表情包）可以只发送图片，其他类型必须同时有图片和文字
    if (type === 18) {
      if (!hasImage) {
        message.error('请先上传图片');
        return;
      }
    } else {
      // type=17、19需要同时有图片和文字
      if (!hasImage) {
        message.error('请先上传图片');
        return;
      }
      if (!hasText) {
        message.error('请输入文字内容');
        return;
      }
    }

    // 调用图片编辑接口
    handleImageEditSubmit(nextContent);
  } else {
    // 普通聊天功能
    if (!nextContent) return;
    onRequest(nextContent);
    content.value = '';
  }
}

// 处理图片编辑提交
const handleImageEditSubmit = async (textContent: string) => {
  if (!sessionId.value) {
    console.error('session_id未获取，无法发送消息');
    message.error('会话未初始化，请刷新页面重试');
    return;
  }

  if (!uploadedImage.value?.url) {
    message.error('请先上传图片');
    return;
  }

  try {
    agentRequestLoading.value = true;

    // 先添加用户消息到聊天记录
    const userMessage = {
      id: `user_${Date.now()}`,
      message: textContent || '',
      status: 'local' as const,
      img_url: uploadedImage.value.url, // 添加图片URL用于显示
    };

    const currentMessages = messages.value;
    setMessages([...currentMessages, userMessage]);

    // 清空输入框和上传的图片
    content.value = '';
    uploadedImage.value = null;
    imageFileList.value = [];

    // 滚动到底部
    await nextTick();
    scrollToBottom();

    const type = Number.parseInt(routeType.value) || 1;

    // 调用图片编辑接口
    const response = await editImageApi({
      type,
      content: textContent || '',
      session_id: sessionId.value,
      img_url: userMessage.img_url,
    });

    // 添加AI回复消息
    if (response) {
      const aiMessage = {
        id: `ai_${Date.now()}`,
        message: `<p align="center"><img src="${response}" alt="编辑后的图片"/></p>`,
        status: 'success' as const,
        isImageMessage: true, // 标识为图片消息
      };

      const updatedMessages = messages.value;
      setMessages([...updatedMessages, aiMessage]);

      // 滚动到底部
      await nextTick();
      scrollToBottom();
    }

  } catch (error) {
    console.error('图片编辑失败:', error);
    message.error('图片编辑失败，请重试');
  } finally {
    agentRequestLoading.value = false;
  }
};

function onAddConversation() {
  // 切换到新建会话
  activeKey.value = 'new';
  // 清空消息列表
  setMessages([]);
  // 清空图片上传状态
  uploadedImage.value = null;
  imageFileList.value = [];
  // 获取新的提示词和session_id
  fetchCueWord();
}

const onConversationClick: ConversationsProps['onActiveChange'] = async (
  key,
) => {
  activeKey.value = key;

  // 查找对应的会话信息
  const conversation = conversationsItems.value.find(
    (item) => item.key === key,
  );

  if (conversation && 'dialogue_id' in conversation) {
    // 历史会话：加载历史消息记录
    const dialogueSessionId = (conversation as any).dialogue_id;
    sessionId.value = dialogueSessionId;
    sessionIdMap.value[key] = dialogueSessionId;

    // 获取历史消息
    await fetchDialogueHistory(dialogueSessionId);
  } else {
    // 新会话：清空消息列表，获取提示词和新的session_id
    setMessages([]);
    sessionId.value = '';
    // 清空图片上传状态
    uploadedImage.value = null;
    imageFileList.value = [];
    await fetchCueWord();

    // 保存新会话的session_id
    if (sessionId.value) {
      sessionIdMap.value[key] = sessionId.value;
    }
  }
};

const handleFileChange: AttachmentsProps['onChange'] = (info) =>
  (attachedFiles.value = info.fileList);

// 图片上传前验证
const beforeImageUpload = (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('只能上传图片格式文件！');
    return false;
  }

  // 检查文件大小（限制为10MB）
  const isValidSize = file.size / 1024 / 1024 < 10;
  if (!isValidSize) {
    message.error('图片文件大小不能超过 10MB！');
    return false;
  }

  return true;
};

// 图片上传状态变化处理
const handleImageUploadChange = (info: { file: UploadFile; fileList: UploadFile[] }) => {
  const { file, fileList: newFileList } = info;

  console.warn('图片上传状态变化:', file.status, file);

  // 更新文件列表
  imageFileList.value = newFileList;

  if (file.status === 'uploading') {
    imageUploading.value = true;
    console.warn('开始上传图片文件:', file.name);
  } else if (file.status === 'done') {
    imageUploading.value = false;

    console.warn('图片上传接口响应:', file.response);

    // 上传成功，更新文件信息
    let imageUrl = '';
    if (file.response) {
      // 根据后端响应格式提取URL
      if (typeof file.response === 'string') {
        imageUrl = file.response;
      } else if (file.response.data) {
        imageUrl = file.response.data;
      } else if (file.response.url) {
        imageUrl = file.response.url;
      }
    }

    if (imageUrl) {
      const fileName = imageUrl.slice(Math.max(0, imageUrl.lastIndexOf('/') + 1));

      uploadedImage.value = {
        ...file,
        name: fileName,
        url: imageUrl,
        status: 'done',
      } as UploadFile;

      console.warn('图片上传成功，文件URL:', imageUrl);
      message.success('图片上传成功');
    } else {
      // 响应格式不正确
      handleImageUploadError(file, '响应格式不正确');
    }
  } else if (file.status === 'error') {
    handleImageUploadError(file, file.error?.message || '上传失败');
  }
};

// 图片上传错误处理
const handleImageUploadError = (file: UploadFile, errorMessage: string) => {
  imageUploading.value = false;

  console.error('图片上传失败:', errorMessage);
  message.error(`图片上传失败: ${errorMessage}`);

  // 保留文件信息但标记为错误状态
  uploadedImage.value = {
    ...file,
    status: 'error',
  } as UploadFile;
};

// 移除图片文件
const handleImageRemove = () => {
  uploadedImage.value = null;
  imageFileList.value = [];
};

// ==================== Nodes ====================
const placeholderNode = computed(() =>
  h(
    Space,
    { direction: 'vertical', size: 16, style: styles.value.placeholder },
    () => [
      // 注释掉欢迎提示
      h(Welcome, {
        variant: 'borderless',
        icon: () =>
          'https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp',
        title: () => `你好！我是${routeTitle.value}`,
        description: () => cueWord.value,
        // extra: () => h(Space, {}, () => [
        //   h(Button, { icon: h(ShareAltOutlined) }),
        //   h(Button, { icon: h(EllipsisOutlined) }),
        // ]),
      }),
      // h(Prompts, {
      //   title: 'Do you want?',
      //   items: placeholderPromptsItems,
      //   styles: {
      //     list: {
      //       width: '100%',
      //     },
      //     item: {
      //       flex: 1,
      //     },
      //   },
      //   onItemClick: onPromptsItemClick,
      // }),
    ],
  ),
);

const items = computed<BubbleListProps['items']>(() => {
  if (messages.value.length === 0) {
    return [{ content: placeholderNode, variant: 'borderless' }];
  }
  return messages.value.map((messageItem) => {
    const { id, message, status } = messageItem;
    // 检查是否为历史消息（通过ID前缀判断）
    const isHistoryMessage = String(id).startsWith('history_');
    // 检查是否为图片消息
    const isImageMessage = (messageItem as any).isImageMessage === true;
    // 检查是否为包含图片的用户消息
    const hasUserImage = status === 'local' && (messageItem as any).img_url;

    let role = 'ai';
    if (status === 'local') {
      // 用户消息：如果包含图片，使用特殊角色
      role = hasUserImage ? 'local_with_image' : 'local';
    } else if (isImageMessage) {
      role = 'ai_image'; // 图片消息使用专门的角色，不包含打字效果
    } else if (isHistoryMessage) {
      // 历史消息中的图片也使用ai_image角色
      role = isImageMessage ? 'ai_image' : 'ai_history';
    }

    return {
      key: id,
      role,
      content: message,
    };
  });
});
</script>

<template>
  <div class="chat-container" :style="styles.layout">
    <div :style="styles.chat">
      <!-- 🌟 消息列表 -->
      <Bubble.List
        :items="items"
        :roles="roles"
        :style="styles.messages"
        :auto-scroll="true"
      />

      <!-- 🌟 提示词 -->
      <!-- <Prompts
        :items="senderPromptsItems"
        @item-click="onPromptsItemClick"
        v-if="!isSmallScreen"
      /> -->

      <!-- 🌟 输入框 -->
      <div :style="styles.sender">
        <Sender
          :value="content"
          :loading="agentRequestLoading"
          @submit="onSubmit"
          @change="(value) => (content = value)"
        >
          <template #prefix>
            <Badge :dot="attachedFiles.length > 0 && !headerOpen">
              <Button type="text" @click="() => (headerOpen = !headerOpen)">
                <template #icon>
                  <PaperClipOutlined />
                </template>
              </Button>
            </Badge>
          </template>

          <template #header>
            <Sender.Header
              :title="shouldShowImageUpload ? '图片上传' : 'Attachments'"
              :open="headerOpen"
              :styles="{ content: { padding: 0 } }"
              @open-change="(open) => (headerOpen = open)"
            >
              <!-- 图片上传功能 -->
              <div v-if="shouldShowImageUpload" style="padding: 16px;">
                <Upload.Dragger
                  :accept="'image/*'"
                  :before-upload="beforeImageUpload"
                  :file-list="imageFileList"
                  :show-upload-list="false"
                  action="https://szr.jiajs.cn/mobile/index/upload"
                  name="file"
                  style="margin-bottom: 16px;"
                  @change="handleImageUploadChange"
                >
                  <div
                    v-if="!uploadedImage && !imageUploading"
                    style="padding: 20px; text-align: center;"
                  >
                    <div style="font-size: 48px; color: #999; margin-bottom: 8px;">
                      📷
                    </div>
                    <div style="font-size: 16px; margin-bottom: 4px;">
                      点击或拖拽图片到此区域上传
                    </div>
                    <div style="font-size: 12px; color: #999;">
                      支持 JPG、PNG、GIF 等格式，文件大小不超过 10MB
                    </div>
                  </div>

                  <div
                    v-else-if="imageUploading"
                    style="padding: 20px; text-align: center;"
                  >
                    <div style="font-size: 48px; color: #1890ff; margin-bottom: 8px;">
                      ⏳
                    </div>
                    <div style="font-size: 16px;">
                      正在上传图片...
                    </div>
                  </div>

                  <div
                    v-else-if="uploadedImage?.status === 'done'"
                    style="padding: 20px; text-align: center;"
                  >
                    <div style="font-size: 48px; color: #52c41a; margin-bottom: 8px;">
                      ✅
                    </div>
                    <div style="font-size: 16px; margin-bottom: 4px;">
                      {{ uploadedImage?.name || '图片文件' }}
                    </div>
                    <div style="font-size: 12px; color: #999; margin-bottom: 8px;">
                      已上传成功
                    </div>
                    <Button
                      type="text"
                      danger
                      size="small"
                      @click.stop="handleImageRemove"
                    >
                      移除
                    </Button>
                  </div>

                  <div
                    v-else-if="uploadedImage?.status === 'error'"
                    style="padding: 20px; text-align: center;"
                  >
                    <div style="font-size: 48px; color: #ff4d4f; margin-bottom: 8px;">
                      ❌
                    </div>
                    <div style="font-size: 16px; margin-bottom: 4px;">
                      {{ uploadedImage?.name || '图片文件' }}
                    </div>
                    <div style="font-size: 12px; color: #ff4d4f; margin-bottom: 8px;">
                      上传失败，请重试
                    </div>
                    <Button
                      type="text"
                      danger
                      size="small"
                      @click.stop="handleImageRemove"
                    >
                      移除
                    </Button>
                  </div>
                </Upload.Dragger>

                <!-- 功能说明 -->
                <div style="font-size: 12px; color: #666; line-height: 1.4;">
                  <div v-if="routeType === '17'">
                    <strong>图片精修：</strong>需要同时上传图片和输入文字描述
                  </div>
                  <div v-else-if="routeType === '18'">
                    <strong>表情包：</strong>可以只上传图片，也可以添加文字描述
                  </div>
                  <div v-else-if="routeType === '19'">
                    <strong>商品抠图：</strong>需要同时上传图片和输入文字描述
                  </div>
                </div>
              </div>

              <!-- 原有的文件附件功能 -->
              <Attachments
                v-else
                :before-upload="() => false"
                :items="attachedFiles"
                @change="handleFileChange"
              >
                <template #placeholder="type">
                  <Flex
                    v-if="type && type.type === 'inline'"
                    align="center"
                    justify="center"
                    vertical
                    gap="2"
                  >
                    <Typography.Text style="font-size: 30px; line-height: 1">
                      <CloudUploadOutlined />
                    </Typography.Text>
                    <Typography.Title
                      :level="5"
                      style="margin: 0; font-size: 14px; line-height: 1.5"
                    >
                      Upload files
                    </Typography.Title>
                    <Typography.Text type="secondary">
                      Click or drag files to this area to upload
                    </Typography.Text>
                  </Flex>
                  <Typography.Text v-if="type && type.type === 'drop'">
                    Drop file here
                  </Typography.Text>
                </template>
              </Attachments>
            </Sender.Header>
          </template>
        </Sender>
      </div>
    </div>

    <div :style="styles.menu">
      <!-- 🌟 Logo -->
      <div :style="styles.logo">
        <img
          src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
          draggable="false"
          alt="logo"
          :style="styles['logo-img']"
        />
        <span :style="styles['logo-span']">{{
          routeTitle || '零号机器人'
        }}</span>
      </div>

      <!-- 🌟 添加会话 -->
      <Button type="link" :style="styles.addBtn" @click="onAddConversation">
        <PlusOutlined />
        创建新会话
      </Button>

      <!-- 🌟 会话管理 -->
      <Conversations
        :items="conversationsItems"
        :style="styles.conversations"
        :active-key="activeKey"
        @active-change="onConversationClick"
      />
    </div>

    <!-- 图片预览弹窗 -->
    <Modal
      v-model:open="imagePreviewVisible"
      title="图片预览"
      :footer="null"
      width="90vw"
      :style="{ maxWidth: '1200px' }"
      centered
      @cancel="closeImagePreview"
      class="image-preview-modal"
    >
      <div class="image-preview-container">
        <img
          v-if="currentPreviewImage.src"
          :src="currentPreviewImage.src"
          :alt="currentPreviewImage.alt"
          :title="currentPreviewImage.title"
          class="preview-image"
        />
      </div>
    </Modal>
  </div>
</template>

<style scoped>
/* 响应式调整 */
@media (max-width: 1200px) {
  .chat-container {
    min-width: 800px;
  }
}

@media (max-width: 900px) {
  .chat-container {
    flex-direction: column;
    min-width: 100%;
  }

  .conversations {
    max-height: 200px;
  }
}

@media (max-width: 600px) {
  .logo-span {
    font-size: 14px !important;
  }

  .addBtn {
    font-size: 12px;
  }

  .messages-container {
    padding: 5px 0;
  }
}

.chat-container {
  position: relative;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
}

:deep(.ant-welcome-icon) {
  flex-shrink: 0;
}

/* Markdown内容样式 - 使用:deep()确保样式穿透 */
:deep(.markdown-content table) {
  width: 100%;
  margin: 12px 0;
  border-spacing: 0;
  border-collapse: collapse;
  border: 1px solid #d9d9d9;
}

:deep(.markdown-content table th),
:deep(.markdown-content table td) {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #d9d9d9;
}

:deep(.markdown-content table th) {
  font-weight: 600;
  background-color: #fafafa;
}

:deep(.markdown-content table tr:nth-child(even)) {
  background-color: #fafafa;
}

:deep(.markdown-content table tr:hover) {
  background-color: #f0f0f0;
}

:deep(.markdown-content pre) {
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
  background-color: #f6f8fa;
  border-radius: 6px;
}

:deep(.markdown-content code) {
  padding: 2px 4px;
  font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: #f6f8fa;
  border-radius: 3px;
}

:deep(.markdown-content pre code) {
  padding: 0;
  background-color: transparent;
}

:deep(.markdown-content blockquote) {
  padding: 0 16px;
  margin: 12px 0;
  color: #666;
  border-left: 4px solid #d9d9d9;
}

:deep(.markdown-content a) {
  color: #1890ff;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin: 16px 0 8px;
  font-weight: 600;
}

:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  padding-left: 24px;
  margin: 8px 0;
}

:deep(.markdown-content li) {
  margin: 4px 0;
}

/* 图片预览相关样式 */
:deep(.markdown-content img) {
  width: auto;
  max-width: 400px;
  height: auto;
  max-height: 300px;
  cursor: pointer;
  object-fit: contain;
  border-radius: 4px;
  transition: opacity 0.2s ease;
}

:deep(.markdown-content img:hover) {
  opacity: 0.8;
}

/* 图片加载状态样式 */
:deep(.image-loading) {
  padding: 8px 12px;
  font-style: italic;
  color: #1890ff;
  background: #f0f8ff;
  border-left: 3px solid #1890ff;
  border-radius: 4px;
}

.image-preview-modal {
  .image-preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 80vh;
    padding: 10px;
    overflow: auto;
  }

  .preview-image {
    width: auto;
    max-width: 100%;
    height: auto;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
}
</style>
